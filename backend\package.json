{"name": "portfoliox-backend", "version": "1.0.0", "description": "Backend server for <PERSON><PERSON><PERSON>'s portfolio contact form", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["portfolio", "contact-form", "express", "mongodb", "nodejs"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "mongoose": "^8.0.3", "resend": "^3.5.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}