# Portfolio Contact System

A complete contact form system with Node.js backend and MongoDB database for Pradeep Yadav's portfolio website.

## Features

### Frontend
- ✅ Minimalist contact form design (consistent with existing portfolio)
- ✅ Real-time client-side validation
- ✅ Form submission with user feedback
- ✅ No animations or transitions (as per user preference)
- ✅ Responsive design for all devices
- ✅ Accessibility features

### Backend
- ✅ Express.js server with security middleware
- ✅ MongoDB database with Mongoose ODM
- ✅ Input validation and sanitization
- ✅ Rate limiting for spam protection
- ✅ Basic spam detection
- ✅ CORS configuration
- ✅ Error handling and logging
- ✅ Email notifications with Nodemailer
- ✅ Professional HTML email templates
- ✅ Email rate limiting and validation
- ✅ Automatic confirmation emails to users

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local installation or MongoDB Atlas account)

### Installation

1. **Navigate to Backend Directory**
   ```bash
   cd backend
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Database Setup**
   
   **Option A: Local MongoDB**
   - Install MongoDB locally
   - Start MongoDB service
   - The app will connect to `mongodb://localhost:27017/portfoliox`

   **Option B: MongoDB Atlas (Cloud)**
   - Create a MongoDB Atlas account
   - Create a new cluster
   - Get your connection string
   - Update the `MONGODB_URI` in `.env` file

4. **Environment Configuration**
   - The `.env` file is already created with default values
   - Update `MONGODB_URI` if using MongoDB Atlas
   - Update `ALLOWED_ORIGINS` for production deployment

   **Email Configuration (Optional):**
   To enable email notifications, configure these variables in `.env`:
   ```env
   # Email SMTP Settings
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Your Name
   TO_EMAIL=<EMAIL>

   # Email Features
   SEND_NOTIFICATION_EMAIL=true
   SEND_CONFIRMATION_EMAIL=true
   EMAIL_RATE_LIMIT_MAX=10
   EMAIL_RATE_LIMIT_WINDOW_MS=3600000
   ```

5. **Start the Server**
   ```bash
   # Development mode (with auto-restart)
   npm run dev
   
   # Production mode
   npm start
   ```

6. **Access the Application**
   - Frontend: http://localhost:3000
   - API: http://localhost:3000/api/contact

## API Endpoints

### POST /api/contact
Submit a new contact form message.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "subject": "Project Inquiry",
  "message": "Hello, I would like to discuss a project..."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Thank you for your message! I will get back to you soon.",
  "data": {
    "id": "contact_id",
    "name": "John Doe",
    "subject": "Project Inquiry",
    "createdAt": "2025-01-15T10:30:00.000Z"
  }
}
```

### GET /api/contact
Get all contact messages (admin endpoint).

**Query Parameters:**
- `status`: Filter by status (new, read, replied, archived)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `includeSpam`: Include spam messages (default: false)

### GET /api/contact/:id
Get a specific contact message by ID.

### GET /api/contact/test-email
Test email configuration (admin endpoint).

**Response (Success):**
```json
{
  "success": true,
  "message": "Email configuration is working correctly",
  "details": {
    "success": true,
    "message": "Email configuration is valid"
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "message": "Email configuration test failed",
  "error": "Authentication failed"
}
```

### GET /api/contact/email-stats
Get email service statistics (admin endpoint).

**Response:**
```json
{
  "success": true,
  "message": "Email service statistics retrieved",
  "data": {
    "sent": 15,
    "failed": 2,
    "rateLimited": 1,
    "isConfigured": true,
    "rateLimitEntries": 3,
    "features": {
      "notificationEmail": true,
      "confirmationEmail": true
    }
  }
}
```

## Database Schema

### Contact Model
```javascript
{
  name: String (required, 2-100 chars),
  email: String (required, valid email),
  subject: String (required, 3-200 chars),
  message: String (required, 10-2000 chars),
  ipAddress: String,
  userAgent: String,
  status: String (new, read, replied, archived),
  isSpam: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

## Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Contact Form Limiting**: 5 submissions per 15 minutes per IP
- **Email Rate Limiting**: 10 emails per hour (configurable)
- **Input Validation**: Server-side validation with express-validator
- **Input Sanitization**: XSS protection with escape functions and email content sanitization
- **Email Validation**: Validates email addresses before sending
- **Spam Detection**: Basic keyword and pattern detection
- **CORS Protection**: Configured allowed origins
- **Helmet.js**: Security headers
- **Email Security**: Content sanitization prevents email injection attacks

## File Structure

```
portfoliox/
├── backend/                 # Backend directory
│   ├── config/
│   │   └── database.js      # MongoDB connection
│   ├── models/
│   │   └── Contact.js       # Contact schema/model
│   ├── routes/
│   │   └── contact.js       # Contact API routes
│   ├── services/
│   │   └── emailService.js  # Email notification service
│   ├── templates/
│   │   ├── notificationEmail.html # Email template for notifications
│   │   └── confirmationEmail.html # Email template for confirmations
│   ├── server.js            # Main server file
│   ├── package.json         # Backend dependencies and scripts
│   ├── .env                 # Environment variables
│   ├── .env.example         # Environment variables template
│   ├── .gitignore           # Git ignore rules
│   ├── EMAIL-SETUP-GUIDE.md # Email configuration guide
│   └── README-CONTACT-SYSTEM.md # This file
├── assets/                  # Static assets
├── index.html              # Frontend (updated with contact form)
├── styles.css              # Styles (updated with form styles)
├── script.js               # Frontend JS (updated with form handling)
└── resume.pdf              # Resume file
```

## Development

### Email Notifications Setup
Email notifications are already implemented! To enable them:

1. **Configure SMTP Settings** in `.env`:
   - Set up Gmail App Password or use another SMTP provider
   - Update `SMTP_USER`, `SMTP_PASS`, `FROM_EMAIL`, `TO_EMAIL`

2. **Enable Email Features**:
   ```env
   SEND_NOTIFICATION_EMAIL=true  # Sends notifications to you
   SEND_CONFIRMATION_EMAIL=true  # Sends confirmations to users
   ```

3. **Test Email Configuration**:
   ```bash
   curl http://localhost:3000/api/contact/test-email
   ```

**Gmail Setup Instructions:**
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password: Google Account → Security → App passwords
3. Use the App Password as `SMTP_PASS` in `.env`

### Adding Authentication
To protect admin endpoints:

1. Install JWT packages: `npm install jsonwebtoken bcryptjs`
2. Create authentication middleware
3. Add login/logout routes
4. Protect GET endpoints

### Deployment
1. Set `NODE_ENV=production` in environment
2. Update `ALLOWED_ORIGINS` with your domain
3. Use a production MongoDB instance
4. Configure reverse proxy (nginx) if needed

## Testing

### Contact Form Testing
1. Fill out the form on the frontend
2. Check browser network tab for API calls
3. Verify data is saved in MongoDB
4. Test validation by submitting invalid data

### Email System Testing
1. **Test Email Configuration:**
   ```bash
   curl http://localhost:3000/api/contact/test-email
   ```

2. **Test Email Statistics:**
   ```bash
   curl http://localhost:3000/api/contact/email-stats
   ```

3. **Test Full Contact Form with Email:**
   ```bash
   curl -X POST http://localhost:3000/api/contact \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Test User",
       "email": "<EMAIL>",
       "subject": "Test Subject",
       "message": "This is a test message to verify email notifications."
     }'
   ```

4. **Check Server Logs:**
   - Look for email sending success/failure messages
   - Monitor rate limiting warnings
   - Check for configuration errors

## Troubleshooting

**MongoDB Connection Issues:**
- Ensure MongoDB is running locally
- Check connection string in `.env`
- Verify network access for MongoDB Atlas

**CORS Issues:**
- Update `ALLOWED_ORIGINS` in `.env`
- Check browser console for CORS errors

**Form Not Submitting:**
- Check browser console for JavaScript errors
- Verify API endpoint is accessible
- Check server logs for errors

**Email Not Sending:**
- Test email configuration: `GET /api/contact/test-email`
- Check SMTP credentials in `.env`
- Verify Gmail App Password is correct
- Check server logs for email errors
- Ensure `SEND_NOTIFICATION_EMAIL` and `SEND_CONFIRMATION_EMAIL` are set to `true`
