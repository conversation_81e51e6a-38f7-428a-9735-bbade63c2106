const { Resend } = require('resend');
require('dotenv').config();

async function testResend() {
    console.log('Testing Resend configuration...');
    
    // Check if API key is configured
    if (!process.env.RESEND_API_KEY) {
        console.error('❌ RESEND_API_KEY not found in environment variables');
        return;
    }
    
    if (!process.env.FROM_EMAIL) {
        console.error('❌ FROM_EMAIL not found in environment variables');
        return;
    }
    
    if (!process.env.TO_EMAIL) {
        console.error('❌ TO_EMAIL not found in environment variables');
        return;
    }
    
    console.log('✅ Environment variables found');
    console.log(`FROM_EMAIL: ${process.env.FROM_EMAIL}`);
    console.log(`TO_EMAIL: ${process.env.TO_EMAIL}`);
    console.log(`API Key: ${process.env.RESEND_API_KEY.substring(0, 10)}...`);
    
    try {
        const resend = new Resend(process.env.RESEND_API_KEY);
        
        const emailData = {
            from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
            to: [process.env.TO_EMAIL],
            subject: 'Resend Test Email',
            html: '<h1>Test Email</h1><p>This is a test email to verify your Resend configuration is working correctly.</p>',
            text: 'This is a test email to verify your Resend configuration is working correctly.'
        };
        
        console.log('📧 Sending test email...');
        const result = await resend.emails.send(emailData);
        
        if (result.data) {
            console.log('✅ Email sent successfully!');
            console.log(`Message ID: ${result.data.id}`);
        } else {
            console.log('❌ Email sending failed');
            console.log('Result:', result);
        }
        
    } catch (error) {
        console.error('❌ Error sending email:', error.message);
        if (error.response) {
            console.error('Response:', error.response.data);
        }
    }
}

testResend();
