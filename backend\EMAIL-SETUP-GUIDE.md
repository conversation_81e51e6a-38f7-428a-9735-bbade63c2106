# Email Notification Setup Guide

This guide will help you configure email notifications for your portfolio contact form.

## Quick Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Configure your email settings in `.env`**

3. **Test the configuration:**
   ```bash
   node test-email.js
   ```

4. **Test via API:**
   ```bash
   curl http://localhost:3000/api/contact/test-email
   ```

## Email Provider Setup

### Gmail (Recommended)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password:**
   - Go to Google Account → Security → App passwords
   - Select "Mail" and your device
   - Copy the generated 16-character password

3. **Configure `.env`:**
   ```env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-16-character-app-password
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Your Name
   TO_EMAIL=<EMAIL>
   ```

### Outlook/Hotmail

1. **Enable 2-Factor Authentication** (recommended)
2. **Configure `.env`:**
   ```env
   SMTP_HOST=smtp-mail.outlook.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-password-or-app-password
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Your Name
   TO_EMAIL=<EMAIL>
   ```

### SendGrid (Professional)

1. **Create a SendGrid account**
2. **Generate an API key**
3. **Configure `.env`:**
   ```env
   SMTP_HOST=smtp.sendgrid.net
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=apikey
   SMTP_PASS=your-sendgrid-api-key
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Your Name
   TO_EMAIL=<EMAIL>
   ```

### Mailgun (Professional)

1. **Create a Mailgun account**
2. **Add and verify your domain**
3. **Get SMTP credentials**
4. **Configure `.env`:**
   ```env
   SMTP_HOST=smtp.mailgun.org
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=your-mailgun-smtp-username
   SMTP_PASS=your-mailgun-smtp-password
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Your Name
   TO_EMAIL=<EMAIL>
   ```

## Email Features Configuration

### Notification Email (to you)
Sends you an email when someone submits the contact form.

```env
SEND_NOTIFICATION_EMAIL=true
TO_EMAIL=<EMAIL>
```

### Confirmation Email (to user)
Sends a confirmation email to users who submit the form.

```env
SEND_CONFIRMATION_EMAIL=true
FROM_NAME=Your Name
```

### Rate Limiting
Prevents email spam by limiting the number of emails sent.

```env
EMAIL_RATE_LIMIT_MAX=10              # Max emails per window
EMAIL_RATE_LIMIT_WINDOW_MS=3600000   # Window in milliseconds (1 hour)
```

## Testing Your Setup

### 1. Test Email Configuration
```bash
cd backend
node test-email.js
```

### 2. Test via API Endpoint
```bash
curl http://localhost:3000/api/contact/test-email
```

### 3. Test Full Contact Form
```bash
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Test Subject",
    "message": "This is a test message."
  }'
```

## Troubleshooting

### Common Issues

**"Invalid login" or "Authentication failed"**
- Check your email and password
- For Gmail, ensure you're using an App Password, not your regular password
- Verify 2FA is enabled for Gmail

**"Email service not configured"**
- Check that all required environment variables are set
- Ensure `.env` file is in the backend directory
- Restart the server after changing `.env`

**"Connection timeout"**
- Check your internet connection
- Verify SMTP host and port are correct
- Some networks block SMTP ports (try different network)

**Emails not being sent**
- Check server logs for error messages
- Verify rate limiting isn't blocking emails
- Test with the `/test-email` endpoint first

### Debug Mode

Enable detailed logging by adding to your `.env`:
```env
NODE_ENV=development
```

### Security Notes

- Never commit your `.env` file to version control
- Use App Passwords instead of regular passwords when possible
- Consider using professional email services (SendGrid, Mailgun) for production
- Monitor your email sending limits to avoid being blocked

## Email Templates

The system includes two professional HTML email templates:

1. **Notification Email** (`templates/notificationEmail.html`)
   - Sent to you when someone contacts you
   - Includes all form data and metadata
   - Highlights potential spam

2. **Confirmation Email** (`templates/confirmationEmail.html`)
   - Sent to users who submit the form
   - Professional thank you message
   - Includes your contact information

You can customize these templates by editing the HTML files in the `templates/` directory.

## Production Deployment

For production environments:

1. **Use environment variables** instead of `.env` file
2. **Use professional email services** (SendGrid, Mailgun)
3. **Set up proper DNS records** (SPF, DKIM, DMARC)
4. **Monitor email delivery** and bounce rates
5. **Set `NODE_ENV=production`** for better error handling

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review server logs for error messages
3. Test with the provided test scripts
4. Verify your email provider's documentation
