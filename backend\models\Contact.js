const mongoose = require('mongoose');

const contactSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Name is required'],
        trim: true,
        minlength: [2, 'Name must be at least 2 characters long'],
        maxlength: [100, 'Name cannot exceed 100 characters']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        trim: true,
        lowercase: true,
        match: [
            /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            'Please provide a valid email address'
        ],
        maxlength: [255, 'Email cannot exceed 255 characters']
    },
    subject: {
        type: String,
        required: [true, 'Subject is required'],
        trim: true,
        minlength: [3, 'Subject must be at least 3 characters long'],
        maxlength: [200, 'Subject cannot exceed 200 characters']
    },
    message: {
        type: String,
        required: [true, 'Message is required'],
        trim: true,
        minlength: [10, 'Message must be at least 10 characters long'],
        maxlength: [2000, 'Message cannot exceed 2000 characters']
    },
    ipAddress: {
        type: String,
        required: false
    },
    userAgent: {
        type: String,
        required: false
    },
    status: {
        type: String,
        enum: ['new', 'read', 'replied', 'archived'],
        default: 'new'
    },
    isSpam: {
        type: Boolean,
        default: false
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better query performance
contactSchema.index({ email: 1 });
contactSchema.index({ createdAt: -1 });
contactSchema.index({ status: 1 });
contactSchema.index({ isSpam: 1 });

// Virtual for formatted date
contactSchema.virtual('formattedDate').get(function() {
    return this.createdAt.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
});

// Pre-save middleware to update the updatedAt field
contactSchema.pre('save', function(next) {
    if (this.isModified() && !this.isNew) {
        this.updatedAt = Date.now();
    }
    next();
});

// Static method to get recent contacts
contactSchema.statics.getRecent = function(limit = 10) {
    return this.find({ isSpam: false })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('name email subject createdAt status');
};

// Static method to get contacts by status
contactSchema.statics.getByStatus = function(status) {
    return this.find({ status, isSpam: false })
        .sort({ createdAt: -1 })
        .select('name email subject createdAt');
};

// Instance method to mark as read
contactSchema.methods.markAsRead = function() {
    this.status = 'read';
    return this.save();
};

// Instance method to mark as spam
contactSchema.methods.markAsSpam = function() {
    this.isSpam = true;
    this.status = 'archived';
    return this.save();
};

module.exports = mongoose.model('Contact', contactSchema);
