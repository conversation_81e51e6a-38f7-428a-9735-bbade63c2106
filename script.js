// ===== PORTFOLIO WEBSITE JAVASCRIPT =====

// DOM Elements
const nav = document.getElementById('nav');
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const sections = document.querySelectorAll('section');

// ===== MOBILE NAVIGATION =====
function toggleMobileNav() {
    navMenu.classList.toggle('active');
    navToggle.classList.toggle('active');

    // Prevent body scroll when menu is open
    if (navMenu.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

function closeMobileNav() {
    navMenu.classList.remove('active');
    navToggle.classList.remove('active');
    document.body.style.overflow = '';
}

// Event listeners for mobile navigation
navToggle.addEventListener('click', toggleMobileNav);

// Close mobile nav when clicking on a link
navLinks.forEach(link => {
    link.addEventListener('click', closeMobileNav);
});

// Close mobile nav when clicking outside
document.addEventListener('click', (e) => {
    if (!nav.contains(e.target) && navMenu.classList.contains('active')) {
        closeMobileNav();
    }
});



// ===== ACTIVE NAVIGATION HIGHLIGHTING =====
function updateActiveNavLink() {
    const scrollPosition = window.scrollY + nav.offsetHeight + 100;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${sectionId}`) {
                    link.classList.add('active');
                }
            });
        }
    });
}

// ===== NAVBAR SCROLL EFFECT =====
function handleNavbarScroll() {
    // Minimalist design - subtle border change on scroll
    if (window.scrollY > 50) {
        nav.style.borderBottomColor = '#000000';
        nav.style.backgroundColor = 'rgba(255, 255, 255, 1)';
    } else {
        nav.style.borderBottomColor = '#e0e0e0';
        nav.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
    }
}



// ===== SCROLL EVENT LISTENERS =====
function handleScroll() {
    updateActiveNavLink();
    handleNavbarScroll();
}

window.addEventListener('scroll', handleScroll);

// ===== KEYBOARD NAVIGATION =====
document.addEventListener('keydown', (e) => {
    // ESC key closes mobile menu
    if (e.key === 'Escape' && navMenu.classList.contains('active')) {
        closeMobileNav();
    }

    // Arrow keys for navigation (when focused on nav links)
    if (document.activeElement.classList.contains('nav-link')) {
        const currentIndex = Array.from(navLinks).indexOf(document.activeElement);

        if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
            e.preventDefault();
            const nextIndex = (currentIndex + 1) % navLinks.length;
            navLinks[nextIndex].focus();
        } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
            e.preventDefault();
            const prevIndex = (currentIndex - 1 + navLinks.length) % navLinks.length;
            navLinks[prevIndex].focus();
        }
    }
});

// ===== INSTANT SCROLLING =====
function instantScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        const navHeight = nav.offsetHeight;
        const elementPosition = element.offsetTop - navHeight;

        window.scrollTo({
            top: elementPosition,
            behavior: 'auto'
        });
    }
}

// Add instant scrolling to navigation links
navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = link.getAttribute('href');
        if (target.startsWith('#')) {
            instantScroll(target);
        }
    });
});

// Add instant scrolling to footer "Back to Top" link
const footerTop = document.querySelector('.footer-top');
if (footerTop) {
    footerTop.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'auto'
        });
    });
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    // Set initial active nav link
    updateActiveNavLink();
});

// ===== PERFORMANCE OPTIMIZATION =====
// Debounce function for scroll events (simplified since no animations)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        setTimeout(later, wait);
    };
}

// Use debounced scroll handler for better performance
const debouncedScrollHandler = debounce(handleScroll, 10);
window.removeEventListener('scroll', handleScroll);
window.addEventListener('scroll', debouncedScrollHandler);

// ===== ACCESSIBILITY ENHANCEMENTS =====
// Skip to main content functionality
function addSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--primary-color);
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1001;
    `;

    skipLink.addEventListener('focus', () => {
        skipLink.style.top = '6px';
    });

    skipLink.addEventListener('blur', () => {
        skipLink.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
}

// Initialize accessibility features
addSkipLink();

// Announce page changes to screen readers
function announcePageChange(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
    `;
    announcement.textContent = message;
    document.body.appendChild(announcement);

    // Remove announcement after 1 second (no animation)
    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
}

// Announce section changes when navigating
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        const sectionName = link.textContent;
        // Announce immediately (no delay needed without animations)
        announcePageChange(`Navigated to ${sectionName} section`);
    });
});

// ===== CONTACT FORM HANDLING =====
const contactForm = document.getElementById('contact-form');
const formStatus = document.getElementById('form-status');

// Form validation functions
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateField(fieldId, value, validationType = 'required') {
    const errorElement = document.getElementById(`${fieldId}-error`);
    let isValid = true;
    let errorMessage = '';

    // Clear previous error
    errorElement.classList.remove('show');
    errorElement.textContent = '';

    if (validationType === 'required' && !value.trim()) {
        isValid = false;
        errorMessage = 'This field is required.';
    } else if (validationType === 'email' && value.trim() && !validateEmail(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address.';
    } else if (validationType === 'minLength' && value.trim().length < 10) {
        isValid = false;
        errorMessage = 'Message must be at least 10 characters long.';
    }

    if (!isValid) {
        errorElement.textContent = errorMessage;
        errorElement.classList.add('show');
    }

    return isValid;
}

// Real-time validation
function setupRealTimeValidation() {
    const nameField = document.getElementById('name');
    const emailField = document.getElementById('email');
    const subjectField = document.getElementById('subject');
    const messageField = document.getElementById('message');

    nameField.addEventListener('blur', () => {
        validateField('name', nameField.value, 'required');
    });

    emailField.addEventListener('blur', () => {
        validateField('email', emailField.value, 'email');
    });

    subjectField.addEventListener('blur', () => {
        validateField('subject', subjectField.value, 'required');
    });

    messageField.addEventListener('blur', () => {
        validateField('message', messageField.value, 'minLength');
    });
}

// Form submission
async function handleFormSubmission(e) {
    e.preventDefault();

    const formData = new FormData(contactForm);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        subject: formData.get('subject'),
        message: formData.get('message')
    };

    // Validate all fields
    const isNameValid = validateField('name', data.name, 'required');
    const isEmailValid = validateField('email', data.email, 'email');
    const isSubjectValid = validateField('subject', data.subject, 'required');
    const isMessageValid = validateField('message', data.message, 'minLength');

    if (!isNameValid || !isEmailValid || !isSubjectValid || !isMessageValid) {
        showFormStatus('Please correct the errors above.', 'error');
        return;
    }

    // Show loading state
    const submitButton = contactForm.querySelector('.form-submit');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Sending...';
    submitButton.disabled = true;

    try {
        const response = await fetch('/api/contact', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        // Check if response is ok
        if (!response.ok) {
            // Try to get error message from response
            let errorMessage = 'Something went wrong. Please try again.';
            try {
                const errorResult = await response.json();
                errorMessage = errorResult.message || errorResult.error || errorMessage;
            } catch (parseError) {
                // If we can't parse the error response, use status text
                errorMessage = `Server error: ${response.status} ${response.statusText}`;
            }
            showFormStatus(errorMessage, 'error');
            return;
        }

        const result = await response.json();
        showFormStatus('Thank you! Your message has been sent successfully.', 'success');
        contactForm.reset();
        clearAllErrors();

    } catch (error) {
        console.error('Form submission error:', error);

        // Provide more specific error messages
        let errorMessage = 'Network error. Please check your connection and try again.';

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorMessage = 'Unable to connect to server. Please check your internet connection.';
        } else if (error.name === 'AbortError') {
            errorMessage = 'Request timed out. Please try again.';
        } else if (error.message.includes('CORS')) {
            errorMessage = 'Connection blocked. Please contact the site administrator.';
        }

        showFormStatus(errorMessage, 'error');
    } finally {
        // Reset button state
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
}

function showFormStatus(message, type) {
    formStatus.textContent = message;
    formStatus.className = `form-status ${type}`;

    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            formStatus.className = 'form-status';
        }, 5000);
    }
}

function clearAllErrors() {
    const errorElements = document.querySelectorAll('.form-error');
    errorElements.forEach(error => {
        error.classList.remove('show');
        error.textContent = '';
    });
}

// Initialize contact form
if (contactForm) {
    contactForm.addEventListener('submit', handleFormSubmission);
    setupRealTimeValidation();
}