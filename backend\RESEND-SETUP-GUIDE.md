# Resend Email Setup Guide

This guide will help you configure email notifications for your portfolio contact form using Resend, a modern email API service.

## Why Resend?

- **Modern API**: Simple, developer-friendly email API
- **Better Deliverability**: Higher email delivery rates than traditional SMTP
- **Domain Verification**: Professional emails from your own domain
- **Generous Free Tier**: 3,000 emails/month for free
- **No SMTP Configuration**: No need to manage SMTP settings

## Quick Setup

1. **Sign up for Resend** and verify your domain
2. **Get your API key** from Resend dashboard
3. **Configure environment variables** in `.env`
4. **Test the configuration**

## Step-by-Step Setup

### Step 1: Create Resend Account
1. Go to [resend.com](https://resend.com)
2. Sign up for a free account
3. Verify your email address

### Step 2: Add and Verify Your Domain
1. In Resend dashboard, go to **Domains**
2. Click **Add Domain**
3. Enter your domain (e.g., `pradeepyadav.space`)
4. Resend will provide DNS records to add to your domain

### Step 3: Configure DNS Records
Add these records to your domain registrar (e.g., Hostinger):

**TXT Record (Domain Verification)**:
- Type: `TXT`
- Name: `@`
- Value: `resend-verification=xxxxx` (provided by Resend)

**MX Record (Email Delivery)**:
- Type: `MX`
- Name: `@`
- Value: `feedback-smtp.us-east-1.amazonses.com`
- Priority: `10`

**CNAME Record (DKIM)**:
- Type: `CNAME`
- Name: `xxx._domainkey` (provided by Resend)
- Value: `xxx.dkim.amazonses.com` (provided by Resend)

**TXT Record (SPF)**:
- Type: `TXT`
- Name: `@`
- Value: `v=spf1 include:amazonses.com ~all`

### Step 4: Get API Key
1. Go to [resend.com/api-keys](https://resend.com/api-keys)
2. Click **Create API Key**
3. Give it a name (e.g., "Portfolio Contact Form")
4. Copy the API key (starts with `re_`)

### Step 5: Configure Environment Variables
Update your `.env` file:

```env
# Email Configuration - Resend
RESEND_API_KEY=re_your_actual_api_key_here
FROM_EMAIL=<EMAIL>
FROM_NAME=Your Name
TO_EMAIL=<EMAIL>

# Email Features
SEND_NOTIFICATION_EMAIL=true
SEND_CONFIRMATION_EMAIL=true
EMAIL_RATE_LIMIT_MAX=10
EMAIL_RATE_LIMIT_WINDOW_MS=3600000
```

### Step 6: Test Configuration
1. **Start your server**:
   ```bash
   npm run dev
   ```

2. **Test email configuration**:
   ```bash
   curl http://localhost:3000/api/contact/test-email
   ```

3. **Submit a test contact form** to verify both notification and confirmation emails work

## Troubleshooting

### Domain Not Verified
- Check DNS records are correctly added
- Wait up to 48 hours for DNS propagation
- Use Resend's DNS checker in the dashboard

### API Key Issues
- Ensure API key starts with `re_`
- Check API key has correct permissions
- Regenerate API key if needed

### Email Not Sending
- Check server logs for error messages
- Verify `FROM_EMAIL` uses your verified domain
- Ensure rate limits aren't exceeded

### Testing with Sandbox Domain
For immediate testing while waiting for domain verification:
```env
FROM_EMAIL=<EMAIL>
```
Note: Sandbox emails only deliver to verified email addresses.

## Email Templates

The system uses HTML templates located in `backend/templates/`:
- `notificationEmail.html` - Email sent to you when someone contacts you
- `confirmationEmail.html` - Email sent to the person who contacted you

## Rate Limiting

Email sending is rate-limited to prevent abuse:
- **Notification emails**: Limited per IP address
- **Confirmation emails**: Limited per email address
- **Global limit**: 10 emails per hour by default

Configure limits in `.env`:
```env
EMAIL_RATE_LIMIT_MAX=10
EMAIL_RATE_LIMIT_WINDOW_MS=3600000
```

## Production Deployment

1. **Use your verified domain** for `FROM_EMAIL`
2. **Set strong rate limits** to prevent abuse
3. **Monitor email statistics** via the API
4. **Set up email monitoring** in Resend dashboard

## API Endpoints

- `GET /api/contact/test-email` - Test email configuration
- `GET /api/contact/stats` - Get email statistics
- `POST /api/contact` - Submit contact form (triggers emails)

## Support

- **Resend Documentation**: [resend.com/docs](https://resend.com/docs)
- **Resend Support**: [resend.com/support](https://resend.com/support)
- **DNS Help**: Contact your domain registrar (Hostinger)
