# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=3000

# Database Configuration
# For local development (MongoDB running locally)
# MONGODB_URI=mongodb://localhost:27017/portfoliox

# For MongoDB Atlas (cloud database) - replace with your connection string
MONGODB_URI=your-mongodb-connection-string-here

# Security Configuration
# Add your production domain here when deploying
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000,http://127.0.0.1:3000,http://localhost:5500

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CONTACT_RATE_LIMIT_MAX=5

# Email Configuration - Resend
# Get your API key from https://resend.com/api-keys
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx
FROM_EMAIL=<EMAIL>
FROM_NAME=Your Name
TO_EMAIL=<EMAIL>

# Email Features
SEND_NOTIFICATION_EMAIL=true
SEND_CONFIRMATION_EMAIL=true
EMAIL_RATE_LIMIT_MAX=10
EMAIL_RATE_LIMIT_WINDOW_MS=3600000

# Resend Setup Instructions:
# 1. Sign up at https://resend.com
# 2. Verify your domain in the Resend dashboard
# 3. Get your API key from https://resend.com/api-keys
# 4. Set FROM_EMAIL to use your verified domain (e.g., <EMAIL>)
# 5. For testing, you can use Resend's sandbox domain: <EMAIL>
#
# Domain Verification:
# - Add DNS records provided by Resend to your domain registrar
# - Wait for DNS propagation (up to 48 hours)
# - Verify domain status in Resend dashboard
