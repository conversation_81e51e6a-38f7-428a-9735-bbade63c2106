const express = require('express');
const { body, validationResult } = require('express-validator');
const Contact = require('../models/Contact');
const emailService = require('../services/emailService');

const router = express.Router();

// Validation rules
const contactValidation = [
    body('name')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Name must be between 2 and 100 characters')
        .matches(/^[a-zA-Z\s'-]+$/)
        .withMessage('Name can only contain letters, spaces, hyphens, and apostrophes'),
    
    body('email')
        .trim()
        .isEmail()
        .withMessage('Please provide a valid email address')
        .normalizeEmail()
        .isLength({ max: 255 })
        .withMessage('Email cannot exceed 255 characters'),
    
    body('subject')
        .trim()
        .isLength({ min: 3, max: 200 })
        .withMessage('Subject must be between 3 and 200 characters')
        .escape(),
    
    body('message')
        .trim()
        .isLength({ min: 10, max: 2000 })
        .withMessage('Message must be between 10 and 2000 characters')
        .escape()
];

// Simple spam detection
const detectSpam = (data) => {
    const spamKeywords = [
        'viagra', 'casino', 'lottery', 'winner', 'congratulations',
        'click here', 'free money', 'make money fast', 'work from home',
        'guaranteed', 'no risk', 'limited time', 'act now'
    ];
    
    const text = `${data.subject} ${data.message}`.toLowerCase();
    
    // Check for spam keywords
    const hasSpamKeywords = spamKeywords.some(keyword => text.includes(keyword));
    
    // Check for excessive links
    const linkCount = (text.match(/http[s]?:\/\//g) || []).length;
    const hasExcessiveLinks = linkCount > 3;
    
    // Check for excessive caps
    const capsPercentage = (text.match(/[A-Z]/g) || []).length / text.length;
    const hasExcessiveCaps = capsPercentage > 0.5 && text.length > 20;
    
    return hasSpamKeywords || hasExcessiveLinks || hasExcessiveCaps;
};

// POST /api/contact - Submit contact form
router.post('/', contactValidation, async (req, res) => {
    try {
        // Check for validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { name, email, subject, message } = req.body;

        // Get client information
        const ipAddress = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
        const userAgent = req.get('User-Agent') || '';

        // Check for spam
        const isSpam = detectSpam({ subject, message });

        // Create new contact
        const contact = new Contact({
            name,
            email,
            subject,
            message,
            ipAddress,
            userAgent,
            isSpam
        });

        // Save to database
        await contact.save();

        // Log the submission (without sensitive data)
        console.log(`New contact submission from ${email} - Subject: ${subject}`);

        if (isSpam) {
            console.log('Message flagged as potential spam');
        }

        // Send email notifications (non-blocking)
        // Don't await this to avoid blocking the response
        emailService.sendEmails(contact.toObject())
            .then(emailResults => {
                console.log('Email sending initiated:', {
                    notification: emailResults.notification.success,
                    confirmation: emailResults.confirmation.success
                });
            })
            .catch(emailError => {
                console.error('Email sending failed:', emailError.message);
                // Don't fail the request if email fails
            });

        res.status(201).json({
            success: true,
            message: 'Thank you for your message! I will get back to you soon.',
            data: {
                id: contact._id,
                name: contact.name,
                subject: contact.subject,
                createdAt: contact.createdAt
            }
        });

    } catch (error) {
        console.error('Contact form submission error:', error);

        // Handle specific MongoDB errors
        if (error.name === 'ValidationError') {
            const validationErrors = Object.values(error.errors).map(err => ({
                field: err.path,
                message: err.message
            }));

            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: validationErrors
            });
        }

        // Handle duplicate key errors (if any unique indexes exist)
        if (error.code === 11000) {
            return res.status(400).json({
                success: false,
                message: 'Duplicate submission detected'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again later.'
        });
    }
});

// GET /api/contact - Get all contacts (admin only - you can add authentication later)
router.get('/', async (req, res) => {
    try {
        const { status, page = 1, limit = 10, includeSpam = false } = req.query;

        const query = includeSpam === 'true' ? {} : { isSpam: false };

        if (status) {
            query.status = status;
        }

        const options = {
            page: parseInt(page),
            limit: parseInt(limit),
            sort: { createdAt: -1 },
            select: 'name email subject status createdAt isSpam'
        };

        const contacts = await Contact.find(query)
            .sort(options.sort)
            .limit(options.limit * 1)
            .skip((options.page - 1) * options.limit)
            .select(options.select);

        const total = await Contact.countDocuments(query);

        res.json({
            success: true,
            data: contacts,
            pagination: {
                page: options.page,
                limit: options.limit,
                total,
                pages: Math.ceil(total / options.limit)
            }
        });

    } catch (error) {
        console.error('Error fetching contacts:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching contacts'
        });
    }
});

// GET /api/contact/test-email - Test email configuration (admin only)
router.get('/test-email', async (req, res) => {
    try {
        const testResult = await emailService.testConfiguration();

        if (testResult.success) {
            res.json({
                success: true,
                message: 'Email configuration is working correctly',
                details: testResult
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Email configuration test failed',
                error: testResult.message
            });
        }
    } catch (error) {
        console.error('Email test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test email configuration',
            error: error.message
        });
    }
});

// GET /api/contact/email-stats - Get email service statistics (admin only)
router.get('/email-stats', async (req, res) => {
    try {
        const stats = emailService.getStats();

        res.json({
            success: true,
            message: 'Email service statistics retrieved',
            data: stats
        });
    } catch (error) {
        console.error('Email stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get email statistics',
            error: error.message
        });
    }
});

// GET /api/contact/:id - Get specific contact (admin only)
router.get('/:id', async (req, res) => {
    try {
        const contact = await Contact.findById(req.params.id);

        if (!contact) {
            return res.status(404).json({
                success: false,
                message: 'Contact not found'
            });
        }

        // Mark as read when viewed
        if (contact.status === 'new') {
            await contact.markAsRead();
        }

        res.json({
            success: true,
            data: contact
        });

    } catch (error) {
        console.error('Error fetching contact:', error);

        if (error.name === 'CastError') {
            return res.status(400).json({
                success: false,
                message: 'Invalid contact ID'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Error fetching contact'
        });
    }
});

module.exports = router;
